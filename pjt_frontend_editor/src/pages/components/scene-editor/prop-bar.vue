<template>
  <div class="prop-bar">
    <!-- 属性 -->
    <div class="props">
      <template v-for="prop in propFields">
        <div :class="{ 'prop-item': true, 'focus-prop-item': isFocusProp(prop.name) }" @mousewheel="handlePlayerMouseWheel(prop.name, $event)">
          <div class="prop-label" :title="prop.title" @click="handleFocusProp(prop.name)">
            <div class="inner-text">{{prop.label}}</div>
          </div>
          <div class="prop-value">
            <template v-if="prop.type === 'Number'">
              <el-input-number size="small" v-model="propValues[prop.name]"
                               :readonly="prop.readonly" :controls="false"
                               @change="handlePlayerChangeProp(prop.name, $event)"
              />
            </template>
            <template v-if="prop.type === 'Boolean'">
              <el-checkbox size="small" v-model="propValues[prop.name]"
                           :readonly="prop.readonly"
                           @change="handlePlayerChangeProp(prop.name, $event)" />
            </template>
            <template v-else>
              <el-input size="small" v-model="propValues[prop.name]"
                        :readonly="prop.readonly" @change="handlePlayerChangeProp(prop.name, $event)" />
            </template>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'prop-bar',
  data() {
    return {
      propFields: null,
      propValues: {},
    }
  },
  methods: {
    setPropFields(propFields, propValues) {
      this.propFields = propFields;
      this.propValues = propValues;
      console.log('setPropFields', propFields);
    },
    isFocusProp(name) {

    },
    handlePlayerMouseWheel(name, e) {

    },
    handleFocusProp(name) {

    },
    handlePlayerChangeProp(name, e) {

    },
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.prop-bar {
  display: flex;
  flex-direction: column;
  height: 100%;

  .props {
    flex: 1;
    height: 0;
    padding: 8px;
    overflow-y: auto;

    /deep/ .el-tabs__content {
      padding: 5px
    }

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
      transition: background 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }

    .prop-item {
      display: flex;
      align-items: stretch;
      margin-bottom: 8px;
      padding: 6px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        // border-color: rgba(102, 126, 234, 0.5);
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
      }

      .prop-label {
        display: flex;
        align-items: center;
        width: 90px;
        // height: 100%;
        color: #a0a0a0;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        .inner-text {
          width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .prop-value {
        flex: 1;

        input[type="text"],
        input[type="number"] {
          width: 100%;
          background: transparent;
          border: 0;
          border-radius: 6px;
          color: #ffffff;
          font-size: 14px;
          outline: none;
          transition: all 0.3s ease;

          // &:focus {
          //     border-color: rgba(102, 126, 234, 0.8);
          //     box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
          //     background: rgba(0, 0, 0, 0.5);
          // }

          &[readonly] {
            cursor: not-allowed;
            opacity: 0.7;
          }
        }
      }
    }

    .focus-prop-item {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(165, 60, 226, 0.5);
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
    }
  }
}
</style>